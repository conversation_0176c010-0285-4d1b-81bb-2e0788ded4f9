#!/bin/bash
# STM32 Ninja 快速编译脚本

echo "[信息] 开始Ninja高速编译..."

# 计时开始
start_time=$(date +%s%3N)

# 执行编译
NINJA="/d/STM32CubeCLT_1.18.0/Ninja/bin/ninja.exe"
if $NINJA -j $(nproc); then
    # 计时结束
    end_time=$(date +%s%3N)
    duration=$((end_time - start_time))
    
    echo ""
    echo "[成功] 编译完成！用时: ${duration}ms"
    echo ""
    echo "    __  __ ___ ____ _   _ "
    echo "   |  \/  |_ _/ ___| | | |"
    echo "   | |\/| || | |   | | | |"
    echo "   | |  | || | |___| |_| |"
    echo "   |_|  |_|___\____|\___/ "
    echo ""
    echo "   编译任务圆满完成！"
    echo ""
    exit 0
else
    echo ""
    echo "[错误] 编译失败！"
    echo ""
    # 尝试分析错误
    if [ -f build.ninja ]; then
        echo "提示："
        echo "1. 检查是否缺少头文件或宏定义"
        echo "2. 尝试运行 ../scripts/smart_build_v2.sh 进行智能修复"
        echo "3. 检查包含路径是否正确"
    fi
    exit 1
fi
