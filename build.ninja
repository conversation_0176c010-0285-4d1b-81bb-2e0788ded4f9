# Ninja build file for ctrl_c_project
# Device: STM32F407VETx
# Generated by STM32 Ninja Ultimate

builddir = build
target = ctrl_c_project

# Toolchain
cc = arm-none-eabi-gcc
as = arm-none-eabi-gcc
ld = arm-none-eabi-gcc
objcopy = arm-none-eabi-objcopy
size = arm-none-eabi-size

# MCU flags
mcuflags = -mcpu=cortex-m4 -mthumb
mcuflags = $mcuflags -mfpu=fpv4-sp-d16 -mfloat-abi=hard

# Compile flags
cflags = $mcuflags -Wall -fdata-sections -ffunction-sections -g -O0
asflags = $mcuflags -x assembler-with-cpp

# Defines
defines = -DSTM32F407VETx -DSTM32F407xx -DUSE_HAL_DRIVER

# Include paths (51 paths)
includes = -I. -I.. -I../Core/Inc -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../MDK-ARM/.cmsis/device/ARM/ARMCA5/Config -I../MDK-ARM/.cmsis/device/ARM/ARMCA5/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCA7/Config -I../MDK-ARM/.cmsis/device/ARM/ARMCA7/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCA9/Config -I../MDK-ARM/.cmsis/device/ARM/ARMCA9/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM0/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM0plus/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM1/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM23/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM23/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMCM3/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM33/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM33/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMCM35P/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM35P/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMCM4/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM55/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM55/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMCM7/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM85/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM85/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMSC000/Include -I../MDK-ARM/.cmsis/device/ARM/ARMSC300/Include -I../MDK-ARM/.cmsis/device/ARM/ARMv81MML/Include -I../MDK-ARM/.cmsis/device/ARM/ARMv81MML/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMv8MBL/Include -I../MDK-ARM/.cmsis/device/ARM/ARMv8MBL/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMv8MML/Include -I../MDK-ARM/.cmsis/device/ARM/ARMv8MML/Include/Template -I../MDK-ARM/.cmsis/include -I../MDK-ARM/RTE/_ctrl_c_project -I../User -I../User/App -I../User/Driver -I../User/Module/bno08x -I../User/Module/encoder -I../User/Module/grayscale -I../User/Module/jy901s -I../User/Module/motor -I../User/Module/mpu6050 -I../User/Module/oled -I../User/Module/pid -I../User/Module/ringbuffer

# Link flags
ldflags = $mcuflags -specs=nano.specs -specs=nosys.specs
ldflags = $ldflags -Wl,--gc-sections -Wl,--print-memory-usage

# Build rules
rule cc
  command = $cc $cflags $includes $defines -MMD -MF $out.d -c $in -o $out
  description = CC $out
  depfile = $out.d
  deps = gcc

rule as
  command = $as $asflags -c $in -o $out
  description = AS $out

rule link
  command = $ld $ldflags -o $out $in -lc -lm -lnosys
  description = LINK $out

rule hex
  command = $objcopy -O ihex $in $out
  description = HEX $out

rule bin
  command = $objcopy -O binary -S $in $out
  description = BIN $out

# Source files
build $builddir/__Core_Src_dma.o: cc ../Core/Src/dma.c
build $builddir/__Core_Src_gpio.o: cc ../Core/Src/gpio.c
build $builddir/__Core_Src_i2c.o: cc ../Core/Src/i2c.c
build $builddir/__Core_Src_main.o: cc ../Core/Src/main.c
build $builddir/__Core_Src_stm32f4xx_hal_msp.o: cc ../Core/Src/stm32f4xx_hal_msp.c
build $builddir/__Core_Src_stm32f4xx_it.o: cc ../Core/Src/stm32f4xx_it.c
build $builddir/__Core_Src_system_stm32f4xx.o: cc ../Core/Src/system_stm32f4xx.c
build $builddir/__Core_Src_tim.o: cc ../Core/Src/tim.c
build $builddir/__Core_Src_usart.o: cc ../Core/Src/usart.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_cortex.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_dma.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_dma_ex.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_exti.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_flash.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_flash_ex.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_flash_ramfunc.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_gpio.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_i2c.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_i2c_ex.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_pwr.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_pwr_ex.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_rcc.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_rcc_ex.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_tim.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_tim_ex.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c
build $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_uart.o: cc ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c
build $builddir/__User_App_encoder_app.o: cc ../User/App/encoder_app.c
build $builddir/__User_App_gray_app.o: cc ../User/App/gray_app.c
build $builddir/__User_App_key_app.o: cc ../User/App/key_app.c
build $builddir/__User_App_led_app.o: cc ../User/App/led_app.c
build $builddir/__User_App_motor_app.o: cc ../User/App/motor_app.c
build $builddir/__User_App_mpu6050_app.o: cc ../User/App/mpu6050_app.c
build $builddir/__User_App_oled_app.o: cc ../User/App/oled_app.c
build $builddir/__User_App_pid_app.o: cc ../User/App/pid_app.c
build $builddir/__User_App_usart_app.o: cc ../User/App/usart_app.c
build $builddir/__User_Driver_key_driver.o: cc ../User/Driver/key_driver.c
build $builddir/__User_Driver_led_driver.o: cc ../User/Driver/led_driver.c
build $builddir/__User_Module_encoder_encoder_driver.o: cc ../User/Module/encoder/encoder_driver.c
build $builddir/__User_Module_grayscale_hardware_iic.o: cc ../User/Module/grayscale/hardware_iic.c
build $builddir/__User_Module_grayscale_software_iic.o: cc ../User/Module/grayscale/software_iic.c
build $builddir/__User_Module_motor_motor_driver.o: cc ../User/Module/motor/motor_driver.c
build $builddir/__User_Module_mpu6050_mpu6050_hal.o: cc ../User/Module/mpu6050/mpu6050_hal.c
build $builddir/__User_Module_oled_oled.o: cc ../User/Module/oled/oled.c
build $builddir/__User_Module_pid_pid.o: cc ../User/Module/pid/pid.c
build $builddir/__User_Module_ringbuffer_ringbuffer.o: cc ../User/Module/ringbuffer/ringbuffer.c
build $builddir/__User_my_scheduler.o: cc ../User/my_scheduler.c
build $builddir/__User_my_timer.o: cc ../User/my_timer.c

# Converted startup file
build $builddir/startup.o: as startup_gnu.s

# Linker script
ldflags = $ldflags -TSTM32_FLASH.ld

# Link
build $builddir/$target.elf: link $builddir/__Core_Src_dma.o $builddir/__Core_Src_gpio.o $builddir/__Core_Src_i2c.o $builddir/__Core_Src_main.o $builddir/__Core_Src_stm32f4xx_hal_msp.o $builddir/__Core_Src_stm32f4xx_it.o $builddir/__Core_Src_system_stm32f4xx.o $builddir/__Core_Src_tim.o $builddir/__Core_Src_usart.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_cortex.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_dma.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_dma_ex.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_exti.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_flash.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_flash_ex.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_flash_ramfunc.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_gpio.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_i2c.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_i2c_ex.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_pwr.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_pwr_ex.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_rcc.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_rcc_ex.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_tim.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_tim_ex.o $builddir/__Drivers_STM32F4xx_HAL_Driver_Src_stm32f4xx_hal_uart.o $builddir/__User_App_encoder_app.o $builddir/__User_App_gray_app.o $builddir/__User_App_key_app.o $builddir/__User_App_led_app.o $builddir/__User_App_motor_app.o $builddir/__User_App_mpu6050_app.o $builddir/__User_App_oled_app.o $builddir/__User_App_pid_app.o $builddir/__User_App_usart_app.o $builddir/__User_Driver_key_driver.o $builddir/__User_Driver_led_driver.o $builddir/__User_Module_encoder_encoder_driver.o $builddir/__User_Module_grayscale_hardware_iic.o $builddir/__User_Module_grayscale_software_iic.o $builddir/__User_Module_motor_motor_driver.o $builddir/__User_Module_mpu6050_mpu6050_hal.o $builddir/__User_Module_oled_oled.o $builddir/__User_Module_pid_pid.o $builddir/__User_Module_ringbuffer_ringbuffer.o $builddir/__User_my_scheduler.o $builddir/__User_my_timer.o $builddir/startup.o

# Output files
build $builddir/$target.hex: hex $builddir/$target.elf
build $builddir/$target.bin: bin $builddir/$target.elf

rule size
  command = $size $in
  description = SIZE $in

build size: size $builddir/$target.elf
  implicit = $builddir/$target.hex

# Default target
default $builddir/$target.hex size

# Clean
rule clean
  command = rm -rf $builddir *.ld *.s
build clean: clean
