--cpu=Cortex-M4.fp.sp
"ctrl_c_project\startup_stm32f407xx.o"
"ctrl_c_project\main.o"
"ctrl_c_project\gpio.o"
"ctrl_c_project\dma.o"
"ctrl_c_project\i2c.o"
"ctrl_c_project\tim.o"
"ctrl_c_project\usart.o"
"ctrl_c_project\stm32f4xx_it.o"
"ctrl_c_project\stm32f4xx_hal_msp.o"
"ctrl_c_project\stm32f4xx_hal_i2c.o"
"ctrl_c_project\stm32f4xx_hal_i2c_ex.o"
"ctrl_c_project\stm32f4xx_hal_rcc.o"
"ctrl_c_project\stm32f4xx_hal_rcc_ex.o"
"ctrl_c_project\stm32f4xx_hal_flash.o"
"ctrl_c_project\stm32f4xx_hal_flash_ex.o"
"ctrl_c_project\stm32f4xx_hal_flash_ramfunc.o"
"ctrl_c_project\stm32f4xx_hal_gpio.o"
"ctrl_c_project\stm32f4xx_hal_dma_ex.o"
"ctrl_c_project\stm32f4xx_hal_dma.o"
"ctrl_c_project\stm32f4xx_hal_pwr.o"
"ctrl_c_project\stm32f4xx_hal_pwr_ex.o"
"ctrl_c_project\stm32f4xx_hal_cortex.o"
"ctrl_c_project\stm32f4xx_hal.o"
"ctrl_c_project\stm32f4xx_hal_exti.o"
"ctrl_c_project\stm32f4xx_hal_tim.o"
"ctrl_c_project\stm32f4xx_hal_tim_ex.o"
"ctrl_c_project\stm32f4xx_hal_uart.o"
"ctrl_c_project\system_stm32f4xx.o"
"ctrl_c_project\ringbuffer.o"
"ctrl_c_project\hardware_iic.o"
"ctrl_c_project\software_iic.o"
"ctrl_c_project\pid.o"
"ctrl_c_project\motor_driver.o"
"ctrl_c_project\oled.o"
"ctrl_c_project\encoder_driver.o"
"ctrl_c_project\bno08x_hal.o"
"ctrl_c_project\led_driver.o"
"ctrl_c_project\key_driver.o"
"ctrl_c_project\usart_app.o"
"ctrl_c_project\led_app.o"
"ctrl_c_project\key_app.o"
"ctrl_c_project\oled_app.o"
"ctrl_c_project\motor_app.o"
"ctrl_c_project\encoder_app.o"
"ctrl_c_project\pid_app.o"
"ctrl_c_project\gray_app.o"
"ctrl_c_project\bno08x_app.o"
"ctrl_c_project\my_scheduler.o"
"ctrl_c_project\my_timer.o"
--strict --scatter "ctrl_c_project\ctrl_c_project.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "ctrl_c_project.map" -o ctrl_c_project\ctrl_c_project.axf